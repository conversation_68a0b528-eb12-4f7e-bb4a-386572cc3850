import { computed } from 'vue'

export function useClauseParser(text) {
  const parsedClauses = computed(() => {
    const clauses = []
    
    try {
      // 分离风险条款和总结部分
      const [clausesText, summaryText] = text.split('【总结】')
      
      // 改进的正则表达式，更精确地匹配每个部分
      const clausePattern = /(\d+)\.\s*条款内容：([\s\S]*?)\s*风险等级：([\s\S]*?)\s*风险说明：([\s\S]*?)\s*潜在后果：([\s\S]*?)\s*修改建议：([\s\S]*?)\s*实例解释：([\s\S]*?)\s*推理过程：([\s\S]*?)(?=\s*\d+\.\s*条款内容：|$)/g
      
      const clauseMatches = clausesText.matchAll(clausePattern)
      
      for (const match of clauseMatches) {
        clauses.push({
          number: match[1],
          content: match[2].trim(),
          riskLevel: match[3].trim(),
          riskDescription: match[4].trim(),
          consequences: match[5].trim(),
          suggestions: match[6].trim(),
          exampleExplanation: match[7].trim(),
          reasoningProcess: match[8].trim()
        })
      }

      // 添加错误处理和验证
      if (clauses.length === 0) {
        console.warn('未找到任何风险条款，可能是格式不正确')
      }

      // 验证每个条款的完整性
      clauses.forEach((clause, index) => {
        if (!clause.content || !clause.riskLevel || !clause.riskDescription || 
            !clause.consequences || !clause.suggestions || !clause.exampleExplanation || !clause.reasoningProcess) {
          console.warn(`第${index + 1}个条款的某些字段为空，可能解析不完整`)
        }
      })
    } catch (error) {
      console.error('解析分析结果时出错:', error)
    }
    
    return clauses
  })

  const summary = computed(() => {
    try {
      const summaryText = text.split('【总结】')[1]
      
      // 解析总结部分
      const riskLevelMatch = summaryText.match(/1\.\s*总体风险等级：([\s\S]*?)(?=2\.|$)/)
      const mainRisksMatch = summaryText.match(/2\.\s*主要风险点：([\s\S]*?)(?=3\.|$)/)
      const suggestionsMatch = summaryText.match(/3\.\s*建议优先处理：([\s\S]*?)$/)
      
      return {
        riskLevel: riskLevelMatch ? riskLevelMatch[1].trim() : '未知',
        mainRisks: mainRisksMatch ? mainRisksMatch[1].trim() : '无',
        prioritySuggestions: suggestionsMatch ? suggestionsMatch[1].trim() : '无'
      }
    } catch (error) {
      console.error('解析总结时出错:', error)
      return {
        riskLevel: '解析错误',
        mainRisks: '解析错误',
        prioritySuggestions: '解析错误'
      }
    }
  })

  return {
    parsedClauses,
    summary
  }
}

// 新增：解析条款分析结果的函数
export function useClauseAnalysisParser(text) {
  const parsedClauseAnalysis = computed(() => {
    const clauses = []
    
    if (!text) return clauses
    
    try {
      // 尝试解析条款分析结果
      // 假设返回格式为：条款1：内容... 解读：解读内容...
      const clausePattern = /条款(\d+)[：:]\s*([\s\S]*?)(?=条款\d+[：:]|解读[：:]|$)/g
      const interpretationPattern = /解读[：:]\s*([\s\S]*?)(?=条款\d+[：:]|$)/g
      
      // 提取所有条款内容
      const clauseMatches = [...text.matchAll(clausePattern)]
      const interpretationMatches = [...text.matchAll(interpretationPattern)]
      
      // 为每个条款创建对象
      clauseMatches.forEach((match, index) => {
        const clauseNumber = match[1]
        const clauseContent = match[2].trim()
        const interpretation = interpretationMatches[index] ? interpretationMatches[index][1].trim() : '暂无解读'
        
        clauses.push({
          number: clauseNumber,
          title: `第${clauseNumber}条`,
          content: clauseContent,
          interpretation: interpretation,
          badgeClass: getBadgeClass(index)
        })
      })
      
      // 如果没有找到条款，尝试其他格式
      if (clauses.length === 0) {
        // 尝试按段落分割
        const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim())
        paragraphs.forEach((paragraph, index) => {
          if (paragraph.trim()) {
            clauses.push({
              number: index + 1,
              title: `第${index + 1}条`,
              content: paragraph.trim(),
              interpretation: 'AI生成的条款解读',
              badgeClass: getBadgeClass(index)
            })
          }
        })
      }
      
    } catch (error) {
      console.error('解析条款分析结果时出错:', error)
      // 如果解析失败，将整个文本作为一个条款
      clauses.push({
        number: 1,
        title: '合同条款分析',
        content: text,
        interpretation: 'AI生成的合同条款分析结果',
        badgeClass: 'bg-blue-100 text-blue-800'
      })
    }
    
    return clauses
  })

  return {
    parsedClauseAnalysis
  }
}

// 获取徽章样式的辅助函数
function getBadgeClass(index) {
  const badgeClasses = [
    'bg-blue-100 text-blue-800',
    'bg-green-100 text-green-800',
    'bg-yellow-100 text-yellow-800',
    'bg-purple-100 text-purple-800',
    'bg-red-100 text-red-800',
    'bg-indigo-100 text-indigo-800',
    'bg-orange-100 text-orange-800',
    'bg-teal-100 text-teal-800',
    'bg-gray-100 text-gray-800',
    'bg-pink-100 text-pink-800',
    'bg-cyan-100 text-cyan-800',
    'bg-emerald-100 text-emerald-800'
  ]
  return badgeClasses[index % badgeClasses.length]
} 