# 条款分析功能测试指南

## 功能概述
在合同风险分析系统中新增了条款分析功能，用户上传合同文件后，系统会：
1. 进行风险分析（原有功能）
2. 进行条款分析（新增功能）
3. 在"条款摘要列表"区域显示条款分析结果

## 测试步骤

### 1. 启动后端服务
```bash
cd backend
python main.py
```

### 2. 启动前端服务
```bash
cd frontend
npm run dev
```

### 3. 测试流程
1. 打开浏览器访问前端应用
2. 上传一个PDF合同文件
3. 等待分析完成
4. 点击"条款摘要"按钮查看条款分析结果

## 新增的API端点

### POST /api/analyze-clauses
- **请求体**: `{"text": "合同文本内容"}`
- **响应**: `{"result": "条款分析结果"}`

## 新增的前端功能

### 1. API调用
- `frontend/src/api/analysis.js` - 新增 `analyzeContractClauses` 函数

### 2. 状态管理
- `frontend/src/composables/useAnalysis.js` - 新增条款分析状态管理

### 3. 组件更新
- `frontend/src/components/analysis/KeyClausesPanel.vue` - 支持动态条款分析结果
- `frontend/src/components/analysis/composables/useClauseParser.js` - 新增条款分析结果解析

### 4. 进度管理
- `frontend/src/constants/index.js` - 新增条款分析进度消息

## 预期结果
- 上传合同后，系统会依次进行风险分析和条款分析
- 条款分析结果会显示在"条款摘要列表"区域
- 如果条款分析失败，会显示错误信息和重试按钮
- 条款分析结果包含条款内容和通俗解释

## 故障排除
1. 如果条款分析失败，检查后端日志
2. 确保AI服务配置正确
3. 检查网络连接和API密钥 