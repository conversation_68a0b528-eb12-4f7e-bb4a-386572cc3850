<template>
  <div>
    <div class="mb-6">
      <div class="flex items-center mb-4">
        <span class="mr-2 text-2xl">📋</span>
        <h3 class="text-xl font-semibold text-gray-900">条款摘要列表</h3>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
      <p class="mt-2 text-gray-600">正在分析合同条款...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-8">
      <div class="text-red-500 mb-2">
        <svg class="h-8 w-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
        </svg>
      </div>
      <p class="text-gray-600">{{ error }}</p>
      <button 
        @click="$emit('retry')" 
        class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        重试
      </button>
    </div>

    <!-- 条款列表 -->
    <div v-else-if="clauses.length > 0" class="space-y-6">
      <div
        v-for="clause in clauses"
        :key="clause.number"
        class="bg-white border border-gray-200 rounded-lg p-6"
      >
        <div class="flex items-center mb-4">
          <span 
            class="text-xs font-medium px-2.5 py-0.5 rounded-full mr-3"
            :class="clause.badgeClass"
          >
            {{ clause.number }}
          </span>
          <h4 class="text-lg font-medium text-gray-900">{{ clause.title }}</h4>
        </div>
        
        <div class="space-y-4">
          <!-- 条款内容 Block -->
          <div class="bg-gray-50 rounded-lg p-4">
            <h5 class="font-semibold text-gray-800 mb-2 text-base">条款内容</h5>
            <p class="text-gray-700 leading-relaxed" v-html="clause.content"></p>
          </div>

          <!-- 解读 Block -->
          <div class="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-400">
            <h5 class="font-semibold text-blue-800 mb-2 flex items-center text-base">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open h-4 w-4 mr-2">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
              </svg>
              解读
            </h5>
            <p class="text-gray-700 leading-relaxed">{{ clause.interpretation }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="text-center py-8">
      <div class="text-gray-400 mb-2">
        <svg class="h-12 w-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
      </div>
      <p class="text-gray-600">暂无条款分析结果</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useClauseAnalysisParser } from './composables/useClauseParser'

defineOptions({
  name: 'KeyClausesPanel'
})

const props = defineProps({
  clauseAnalysisResult: {
    type: String,
    default: null
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['retry'])

// 使用条款分析解析器
const { parsedClauseAnalysis } = useClauseAnalysisParser(props.clauseAnalysisResult)

// 计算要显示的条款列表
const clauses = computed(() => {
  if (props.clauseAnalysisResult) {
    console.log("*************************")
    console.log(parsedClauseAnalysis.value)
    console.log("*************************")
    return parsedClauseAnalysis.value
  }
  
  // 如果没有条款分析结果，返回默认的静态数据
  return [
    {
      number: 1,
      title: '数据分析有误',
      content: '数据分析有误',
      interpretation: '数据分析有误，就别乱显示了',
      badgeClass: 'bg-blue-100 text-blue-800'
    }
  ]
})
</script> 