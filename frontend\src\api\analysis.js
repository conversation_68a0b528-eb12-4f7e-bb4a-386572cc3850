import axios from 'axios'
import { API_BASE_URL, POLLING_INTERVAL, PROGRESS_CONFIG } from '../constants'

/**
 * 上传文件并开始分析
 * @param {File} file - 要上传的文件
 * @returns {Promise<string>} 返回任务ID
 */
export const uploadFile = async (file) => {
  const formData = new FormData()
  formData.append('file', file)

  const response = await axios.post(`${API_BASE_URL}/api/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })

  return response.data.task_id
}

/**
 * 获取分析结果
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 返回分析结果
 */
export const getAnalysisResult = async (taskId) => {
  const response = await axios.get(`${API_BASE_URL}/api/analysis/${taskId}`)
  return response.data
}

/**
 * 调用条款分析服务
 * @param {string} text - 合同文本内容
 * @returns {Promise<string>} 返回条款分析结果
 */
export const analyzeContractClauses = async (text) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/analyze-clauses`, {
      text: text
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    return response.data.result
  } catch (error) {
    console.error('条款分析失败:', error)
    throw new Error('条款分析失败: ' + error.message)
  }
}

/**
 * 轮询获取分析结果
 * @param {string} taskId - 任务ID
 * @param {Function} onProgress - 进度回调函数
 * @param {Function} onComplete - 完成回调函数
 * @param {Function} onError - 错误回调函数
 * @returns {Function} 返回清除轮询的函数
 */
export const pollAnalysisResult = (taskId, onProgress, onComplete, onError) => {
  let isPolling = true

  const poll = async () => {
    if (!isPolling) return

    try {
      const response = await axios.get(`${API_BASE_URL}/api/analysis/status/${taskId}`)
      const { status, progress, result } = response.data

      if (status === 'completed') {
        onComplete(result)
        return
      }

      if (status === 'failed') {
        onError('分析失败')
        return
      }

      onProgress(progress)

      // 继续轮询
      setTimeout(poll, 2000)
    } catch (error) {
      onError(error.message)
    }
  }

  // 开始轮询
  poll()

  // 返回停止轮询的函数
  return () => {
    isPolling = false
  }
} 