import { ref, onUnmounted } from 'vue'
import { useProgress } from './useProgress'
import { uploadFile, pollAnalysisResult, getAnalysisResult, analyzeContractClauses } from '../api/analysis'

// API基础URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "http://localhost:8000"

export function useAnalysis() {
  // 使用进度管理
  const {
    progress: analysisProgress,
    message: progressMessage,
    isProcessing: isAnalyzing,
    updateProgress,
    updateMessage,
    startProcessing,
    completeProcessing,
    resetProgress
  } = useProgress()

  // 状态变量
  const selectedFile = ref(null)
  const analysisResult = ref(null)
  const clauseAnalysisResult = ref(null)
  const clauseAnalysisError = ref(null)
  const currentTaskId = ref(null)
  const pollingInterval = ref(null)

  // 文件选择处理
  const handleFileSelected = (file) => {
    selectedFile.value = file
  }

  // 开始分析
  const startAnalysis = async () => {
    if (!selectedFile.value) return

    startProcessing()

    try {
      // 上传文件并获取任务ID
      const taskId = await uploadFile(selectedFile.value)
      currentTaskId.value = taskId
      updateProgress(15)
      updateMessage('uploaded')

      // 开始轮询结果
      pollingInterval.value = pollAnalysisResult(
        taskId,
        (progress) => updateProgress(progress),
        async (result) => {
          analysisResult.value = result
          
          // 在风险分析完成后，开始条款分析
          try {
            updateMessage('clauseAnalyzing')
            updateProgress(85)
            
            // 从分析结果中提取合同文本，如果没有则使用默认文本
            const contractText = result.contract_text || result.model_1_result || "合同文本内容"
            
            const clauseResult = await analyzeContractClauses(contractText)
            clauseAnalysisResult.value = clauseResult
            clauseAnalysisError.value = null
            
            updateProgress(95)
            updateMessage('completed')
          } catch (clauseError) {
            console.error("条款分析失败:", clauseError)
            // 条款分析失败不影响整体流程
            clauseAnalysisResult.value = null
            clauseAnalysisError.value = clauseError.message
          }
          
          completeProcessing()
        },
        (error) => {
          alert("分析失败: " + error)
          isAnalyzing.value = false
        }
      )
    } catch (error) {
      console.error("分析请求失败:", error)
      alert("分析请求失败，请重试")
      isAnalyzing.value = false
    }
  }

  // 加载分析结果
  const loadAnalysisResult = async (analysisId) => {
    try {
      const result = await getAnalysisResult(analysisId)
      analysisResult.value = result
      completeProcessing()
    } catch (error) {
      console.error("加载分析结果失败:", error)
      alert("加载分析结果失败，请重试")
      isAnalyzing.value = false
    }
  }

  // 重置分析
  const resetAnalysis = () => {
    selectedFile.value = null
    analysisResult.value = null
    clauseAnalysisResult.value = null
    clauseAnalysisError.value = null
    currentTaskId.value = null
    resetProgress()

    if (pollingInterval.value) {
      pollingInterval.value()
      pollingInterval.value = null
    }
  }

  // 重试条款分析
  const retryClauseAnalysis = async () => {
    if (!analysisResult.value) return
    
    try {
      clauseAnalysisError.value = null
      updateMessage('clauseAnalyzing')
      updateProgress(85)
      
      const contractText = analysisResult.value.contract_text || analysisResult.value.model_1_result || "合同文本内容"
      const clauseResult = await analyzeContractClauses(contractText)
      clauseAnalysisResult.value = clauseResult
      
      updateProgress(95)
      updateMessage('completed')
    } catch (error) {
      console.error("条款分析重试失败:", error)
      clauseAnalysisError.value = error.message
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    if (pollingInterval.value) {
      pollingInterval.value()
    }
  })

  return {
    // 状态
    selectedFile,
    isAnalyzing,
    analysisResult,
    clauseAnalysisResult,
    clauseAnalysisError,
    analysisProgress,
    progressMessage,
    currentTaskId,
    
    // 方法
    handleFileSelected,
    startAnalysis,
    resetAnalysis,
    loadAnalysisResult,
    retryClauseAnalysis
  }
} 