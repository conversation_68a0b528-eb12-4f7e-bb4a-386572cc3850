<template>
  <AnalysisResultPanel
    :analysis-result="analysisResult"
    :clause-analysis-result="clauseAnalysisResult"
    :is-clause-analyzing="isClauseAnalyzing"
    :clause-analysis-error="clauseAnalysisError"
    @reset-analysis="$emit('reset-analysis')"
    @further-analysis="$emit('further-analysis')"
    @retry-clause-analysis="$emit('retry-clause-analysis')"
  />
</template>

<script setup>
import AnalysisResultPanel from '../AnalysisResultPanel.vue'

defineProps({
  analysisResult: {
    type: Object,
    required: true
  },
  clauseAnalysisResult: {
    type: String,
    default: null
  },
  isClauseAnalyzing: {
    type: Boolean,
    default: false
  },
  clauseAnalysisError: {
    type: String,
    default: null
  }
})

defineEmits(['reset-analysis', 'further-analysis', 'retry-clause-analysis'])
</script> 